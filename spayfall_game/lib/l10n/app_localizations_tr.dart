// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get appTitle => 'Spayfall';

  @override
  String get createRoom => 'Oda Oluştur';

  @override
  String get joinRoom => 'Odaya Katıl';

  @override
  String get roomCode => 'Oda Kodu';

  @override
  String get playerName => 'Oyuncu Adı';

  @override
  String get startGame => 'Oyunu Başlat';

  @override
  String get waitingForPlayers => 'Oyuncular bekleniyor...';

  @override
  String get yourRole => 'Rolünüz';

  @override
  String get civilian => 'Sivil';

  @override
  String get spy => 'Casus';

  @override
  String get location => 'Konum';

  @override
  String get timeRemaining => 'Kalan Süre';

  @override
  String get accusePlayer => 'Oyuncuyu Suçla';

  @override
  String get guessLocation => 'Konumu Tahmin Et';

  @override
  String get vote => 'Oy Ver';

  @override
  String get yes => 'Evet';

  @override
  String get no => 'Hayır';

  @override
  String get gameOver => 'Oyun Bitti';

  @override
  String get winner => 'Kazanan';

  @override
  String get score => 'Puan';

  @override
  String get nextRound => 'Sonraki Tur';

  @override
  String get newGame => 'Yeni Oyun';

  @override
  String get settings => 'Ayarlar';

  @override
  String get language => 'Dil';

  @override
  String get english => 'İngilizce';

  @override
  String get turkish => 'Türkçe';

  @override
  String get players => 'Oyuncular';

  @override
  String get round => 'Tur';

  @override
  String get accusation => 'Suçlama';

  @override
  String get voting => 'Oylama';

  @override
  String get spyWins => 'Casus Kazandı!';

  @override
  String get civiliansWin => 'Siviller Kazandı!';

  @override
  String get correctGuess => 'Doğru Tahmin!';

  @override
  String get wrongGuess => 'Yanlış Tahmin!';

  @override
  String get unanimousVote => 'Oybirliği Gerekli';

  @override
  String get accusationFailed => 'Suçlama Başarısız';

  @override
  String get timeUp => 'Süre Doldu!';

  @override
  String get askQuestion => 'Soru Sor';

  @override
  String get answer => 'Cevap';

  @override
  String get question => 'Soru';

  @override
  String get chat => 'Sohbet';

  @override
  String get send => 'Gönder';

  @override
  String get viewSpyLocations => 'Casusun Konumlarını Gör';

  @override
  String get spyLocationsList => 'Casusun Gördüğü Konumlar';

  @override
  String get noLocationsYet => 'Henüz hiç konum görülmedi';

  @override
  String get ultimateSpyGame => 'En İyi Casus Oyunu';

  @override
  String get hostInfo =>
      'Bu odanın ev sahibi olacaksınız. Diğer oyuncuların katılması için oda kodunu paylaşın.';

  @override
  String get joinRoomInfo =>
      'Oda kodu için ev sahibine sorun. Oda kodları 6 karakter uzunluğundadır.';

  @override
  String connectionError(String error) {
    return 'Bağlantı hatası: $error';
  }

  @override
  String get goBack => 'Geri Dön';

  @override
  String roomNotFound(String roomId) {
    return 'Oda bulunamadı\\nOda ID: $roomId';
  }

  @override
  String get roomCodeCopied => 'Oda kodu panoya kopyalandı';

  @override
  String get host => 'Ev Sahibi';

  @override
  String get needMinPlayers => 'Başlamak için en az 4 oyuncu gerekli';

  @override
  String failedToCreateRoom(String error) {
    return 'Oda oluşturulamadı: $error';
  }

  @override
  String failedToJoinRoom(String error) {
    return 'Odaya katılınamadı: $error';
  }

  @override
  String failedToStartGame(String error) {
    return 'Oyun başlatılamadı: $error';
  }

  @override
  String get accusesText => ' suçluyor: ';

  @override
  String get ofBeingSpyText => ' casus olmakla!';

  @override
  String get timeRemainingLabel => 'Kalan Süre';

  @override
  String get enterNameAndCode =>
      'Mevcut bir oyuna katılmak için adınızı ve oda kodunu girin';

  @override
  String get gameAlreadyStarted => 'Oyun zaten başlamış';

  @override
  String get roomIsFull => 'Oda dolu';

  @override
  String get roomNotFoundError => 'Oda bulunamadı';

  @override
  String get viewAllLocations => 'Tüm Konumları Gör';

  @override
  String get accused => 'SUÇLANAN';

  @override
  String get pleaseEnterName => 'Lütfen adınızı girin';

  @override
  String get enterNameToCreate =>
      'Yeni bir oyun odası oluşturmak için adınızı girin';

  @override
  String get whereDoYouThink => 'Nerede olduğunuzu düşünüyorsunuz?';

  @override
  String get cancel => 'İptal';

  @override
  String get changeLanguage => 'Dil Değiştir';

  @override
  String get allLocations => 'Tüm Konumlar';

  @override
  String get close => 'Kapat';

  @override
  String get leaveGame => 'Oyundan Çık?';

  @override
  String get leaveGameConfirm => 'Oyundan çıkmak istediğinizden emin misiniz?';

  @override
  String get leave => 'Çık';

  @override
  String get languageChanged => 'Dil başarıyla değiştirildi';

  @override
  String get selectPlayerToAccuse => 'Casusun kim olduğunu düşünüyorsunuz?';

  @override
  String get youAreAccused => 'Siz suçlanıyorsunuz ve oy veremezsiniz';

  @override
  String failedToAccusePlayer(String error) {
    return 'Oyuncu suçlanamadı: $error';
  }
}
