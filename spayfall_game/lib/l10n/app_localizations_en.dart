// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Spayfall';

  @override
  String get createRoom => 'Create Room';

  @override
  String get joinRoom => 'Join Room';

  @override
  String get roomCode => 'Room Code';

  @override
  String get playerName => 'Player Name';

  @override
  String get startGame => 'Start Game';

  @override
  String get waitingForPlayers => 'Waiting for players...';

  @override
  String get yourRole => 'Your Role';

  @override
  String get civilian => 'Civilian';

  @override
  String get spy => 'Spy';

  @override
  String get location => 'Location';

  @override
  String get timeRemaining => 'Time Remaining';

  @override
  String get accusePlayer => 'Accuse Player';

  @override
  String get guessLocation => 'Guess Location';

  @override
  String get vote => 'Vote';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get gameOver => 'Game Over';

  @override
  String get winner => 'Winner';

  @override
  String get score => 'Score';

  @override
  String get nextRound => 'Next Round';

  @override
  String get newGame => 'New Game';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get turkish => 'Turkish';

  @override
  String get players => 'Players';

  @override
  String get round => 'Round';

  @override
  String get accusation => 'Accusation';

  @override
  String get voting => 'Voting';

  @override
  String get spyWins => 'Spy Wins!';

  @override
  String get civiliansWin => 'Civilians Win!';

  @override
  String get correctGuess => 'Correct Guess!';

  @override
  String get wrongGuess => 'Wrong Guess!';

  @override
  String get unanimousVote => 'Unanimous Vote Required';

  @override
  String get accusationFailed => 'Accusation Failed';

  @override
  String get timeUp => 'Time\'s Up!';

  @override
  String get askQuestion => 'Ask a Question';

  @override
  String get answer => 'Answer';

  @override
  String get question => 'Question';

  @override
  String get chat => 'Chat';

  @override
  String get send => 'Send';

  @override
  String get viewSpyLocations => 'View Spy\'s Locations';

  @override
  String get spyLocationsList => 'Spy\'s Seen Locations';

  @override
  String get noLocationsYet => 'No locations seen yet';

  @override
  String get ultimateSpyGame => 'The Ultimate Spy Game';

  @override
  String get hostInfo =>
      'You will be the host of this room. Share the room code with other players to let them join.';

  @override
  String get joinRoomInfo =>
      'Ask the host for the room code. Room codes are 6 characters long.';

  @override
  String connectionError(String error) {
    return 'Connection error: $error';
  }

  @override
  String get goBack => 'Go Back';

  @override
  String roomNotFound(String roomId) {
    return 'Room not found\\nRoom ID: $roomId';
  }

  @override
  String get roomCodeCopied => 'Room code copied to clipboard';

  @override
  String get host => 'Host';

  @override
  String get needMinPlayers => 'Need at least 4 players to start';

  @override
  String failedToCreateRoom(String error) {
    return 'Failed to create room: $error';
  }

  @override
  String failedToJoinRoom(String error) {
    return 'Failed to join room: $error';
  }

  @override
  String failedToStartGame(String error) {
    return 'Failed to start game: $error';
  }

  @override
  String get accusesText => ' accuses ';

  @override
  String get ofBeingSpyText => ' of being the spy!';

  @override
  String get timeRemainingLabel => 'Time Remaining';

  @override
  String get enterNameAndCode =>
      'Enter your name and room code to join an existing game';

  @override
  String get gameAlreadyStarted => 'Game has already started';

  @override
  String get roomIsFull => 'Room is full';

  @override
  String get roomNotFoundError => 'Room not found';

  @override
  String get viewAllLocations => 'View All Locations';

  @override
  String get accused => 'ACCUSED';

  @override
  String get pleaseEnterName => 'Please enter your name';

  @override
  String get enterNameToCreate => 'Enter your name to create a new game room';

  @override
  String get whereDoYouThink => 'Where do you think you are?';

  @override
  String get cancel => 'Cancel';

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get allLocations => 'All Locations';

  @override
  String get close => 'Close';

  @override
  String get leaveGame => 'Leave Game?';

  @override
  String get leaveGameConfirm => 'Are you sure you want to leave the game?';

  @override
  String get leave => 'Leave';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get selectPlayerToAccuse => 'Who do you think is the spy?';

  @override
  String get youAreAccused => 'You are accused and cannot vote';

  @override
  String failedToAccusePlayer(String error) {
    return 'Failed to accuse player: $error';
  }
}
